import torch
import torch.nn as nn


def volume_preservation_loss(displacement_field):
    """计算体积保持约束损失（雅可比行列式约束）
    Args:
        displacement_field: [H, W, 2] 位移场
    Returns:
        loss: 标量损失值
    """
    H, W = displacement_field.shape[:2]
    
    # 计算位移场的梯度
    # 对x方向位移求x和y方向的梯度
    dx_dx = torch.gradient(displacement_field[:, :, 0], dim=1)[0]  # ∂dx/∂x
    dx_dy = torch.gradient(displacement_field[:, :, 0], dim=0)[0]  # ∂dx/∂y
    
    # 对y方向位移求x和y方向的梯度  
    dy_dx = torch.gradient(displacement_field[:, :, 1], dim=1)[0]  # ∂dy/∂x
    dy_dy = torch.gradient(displacement_field[:, :, 1], dim=0)[0]  # ∂dy/∂y
    
    # 构建雅可比矩阵的行列式
    # J = [[1 + ∂dx/∂x, ∂dx/∂y], [∂dy/∂x, 1 + ∂dy/∂y]]
    # det(J) = (1 + ∂dx/∂x)(1 + ∂dy/∂y) - ∂dx/∂y * ∂dy/∂x
    jacobian_det = (1 + dx_dx) * (1 + dy_dy) - dx_dy * dy_dx
    
    # 计算偏离1的程度（理想情况下雅可比行列式应该接近1）
    volume_loss = torch.mean((jacobian_det - 1.0) ** 2)
    
    return volume_loss


def smoothness_loss(displacement_field):
    """计算平滑性约束损失（拉普拉斯平滑损失）
    Args:
        displacement_field: [H, W, 2] 位移场
    Returns:
        loss: 标量损失值
    """
    H, W = displacement_field.shape[:2]
    
    # 计算二阶导数（拉普拉斯算子）
    # 对x方向位移
    dx = displacement_field[:, :, 0]
    dx_xx = torch.gradient(torch.gradient(dx, dim=1)[0], dim=1)[0]  # ∂²dx/∂x²
    dx_yy = torch.gradient(torch.gradient(dx, dim=0)[0], dim=0)[0]  # ∂²dx/∂y²
    laplacian_dx = dx_xx + dx_yy
    
    # 对y方向位移
    dy = displacement_field[:, :, 1]
    dy_xx = torch.gradient(torch.gradient(dy, dim=1)[0], dim=1)[0]  # ∂²dy/∂x²
    dy_yy = torch.gradient(torch.gradient(dy, dim=0)[0], dim=0)[0]  # ∂²dy/∂y²
    laplacian_dy = dy_xx + dy_yy
    
    # 计算拉普拉斯范数
    smooth_loss = torch.mean(laplacian_dx ** 2) + torch.mean(laplacian_dy ** 2)
    
    return smooth_loss


class PhysicsConstrainedLoss(nn.Module):
    """物理约束损失组合类"""
    
    def __init__(self, sparsity_lambda=0.001, volume_lambda=0.01, smooth_lambda=0.005):
        super().__init__()
        self.sparsity_lambda = sparsity_lambda
        self.volume_lambda = volume_lambda
        self.smooth_lambda = smooth_lambda
        
    def forward(self, bn_loss, alpha_weights, displacement_field):
        """计算总损失
        Args:
            bn_loss: BN统计量对齐损失
            alpha_weights: [num_points] 显著性权重
            displacement_field: [H, W, 2] 位移场
        Returns:
            total_loss: 总损失
            loss_dict: 各项损失的字典
        """
        # L1稀疏化损失
        sparsity_loss = torch.mean(torch.abs(alpha_weights))
        
        # 体积保持损失
        volume_loss = volume_preservation_loss(displacement_field)
        
        # 平滑性损失
        smooth_loss = smoothness_loss(displacement_field)
        
        # 总损失
        total_loss = (bn_loss + 
                     self.sparsity_lambda * sparsity_loss +
                     self.volume_lambda * volume_loss +
                     self.smooth_lambda * smooth_loss)
        
        # 返回损失字典用于监控
        loss_dict = {
            'bn_loss': bn_loss.item(),
            'sparsity_loss': sparsity_loss.item(),
            'volume_loss': volume_loss.item(),
            'smooth_loss': smooth_loss.item(),
            'total_loss': total_loss.item()
        }
        
        return total_loss, loss_dict
